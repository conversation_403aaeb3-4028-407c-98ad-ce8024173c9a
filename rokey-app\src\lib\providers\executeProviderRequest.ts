/**
 * Provider request execution utility
 *
 * This module contains the core logic for executing requests to various LLM providers
 * using RouKey's BYOK (Bring Your Own Key) system.
 */

import { z } from 'zod';
import crypto from 'crypto';

// Define the schema here to avoid circular imports
export const RoKeyChatCompletionRequestSchema = z.object({
  custom_api_config_id: z.string(),
  role: z.string().optional(),
  messages: z.array(z.any()),
  model: z.string().optional(),
  temperature: z.number().optional(),
  max_tokens: z.number().optional(),
  stream: z.boolean().optional(),
  specific_api_key_id: z.string().optional(),
});

// Interface for the result of executeProviderRequest
export interface ProviderCallResult {
  success: boolean;
  response?: Response; // For streams
  responseData?: any; // For non-streams (OpenAI-like format)
  responseHeaders?: Headers;
  status?: number; // Provider HTTP status
  error?: any; // Error object/details from provider or internal
  llmRequestTimestamp: Date;
  llmResponseTimestamp: Date;
}

// Timeout configuration
const TIMEOUT_CONFIG = {
  CLASSIFICATION: 5000,    // 5s for role classification
  LLM_REQUEST: 10000,     // 10s for LLM requests (balanced: fast fail but allows completion)
  CONNECTION: 2000,       // 2s for connection establishment
  SOCKET: 1500,          // 1.5s for socket timeout
  GOOGLE_CLASSIFICATION: 10000, // 10s specifically for Google classification
};

// Response caching
const responseCache = new Map<string, {
  response: any;
  timestamp: number;
  provider: string;
  model: string;
}>();
const RESPONSE_CACHE_TTL = 2 * 60 * 1000; // 2 minutes for LLM responses
const MAX_CACHE_SIZE = 1000; // Prevent memory bloat

// Cache cleanup function
function cleanupResponseCache() {
  if (responseCache.size > MAX_CACHE_SIZE) {
    const entries = Array.from(responseCache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    // Remove oldest 20% of entries
    const toRemove = Math.floor(entries.length * 0.2);
    for (let i = 0; i < toRemove; i++) {
      responseCache.delete(entries[i][0]);
    }
  }
}

// Generate cache key for LLM responses
function generateResponseCacheKey(messages: any[], model: string, temperature?: number): string {
  const messageText = messages.map(m => `${m.role}:${typeof m.content === 'string' ? m.content : JSON.stringify(m.content)}`).join('|');
  const cacheInput = `${model}|${messageText}|${temperature || 0}`;
  return crypto.createHash('md5').update(cacheInput).digest('hex');
}

// Enhanced fetch with timeouts and retry logic
async function robustFetch(
  url: string,
  options: RequestInit,
  maxRetries: number = 3,
  timeoutMs?: number
): Promise<Response> {
  let lastError: any;

  const requestTimeout = timeoutMs || (
    url.includes('generativelanguage.googleapis.com') ? TIMEOUT_CONFIG.GOOGLE_CLASSIFICATION :
    TIMEOUT_CONFIG.LLM_REQUEST
  );

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), requestTimeout);

      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      return response;
    } catch (error: any) {
      lastError = error;
      console.warn(`[robustFetch] Attempt ${attempt + 1}/${maxRetries + 1} failed for ${url}:`, error.message);

      if (attempt < maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, attempt), 5000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
}

// Get direct provider model ID
function getDirectProviderModelId(modelIdInDb: string | null, providerName: string): string | null {
  if (!modelIdInDb) return null;

  // For OpenRouter, return the full model ID
  if (providerName.toLowerCase() === 'openrouter') {
    return modelIdInDb;
  }

  // For other providers, extract the model name after the prefix
  const parts = modelIdInDb.split('/');
  return parts.length > 1 ? parts[parts.length - 1] : modelIdInDb;
}

// Main provider request execution function
export async function executeProviderRequest(
  providerName: string | null,
  modelIdInDb: string | null, // The ID stored in your predefined_models table
  apiKeyToUse: string,      // Decrypted API key
  requestPayload: z.infer<typeof RoKeyChatCompletionRequestSchema> // The original, validated request body
): Promise<ProviderCallResult> {
  let p_llmRequestTimestamp: Date | null = null;
  let p_llmResponseTimestamp: Date | null = new Date();
  let p_status: number | undefined = undefined;
  let p_error: any = undefined;
  let p_response: Response | undefined = undefined;
  let p_responseData: any = undefined;
  let p_responseHeaders: Headers | undefined = undefined;

  // Check response cache for non-streaming requests
  if (!requestPayload.stream && requestPayload.messages && modelIdInDb) {
    const cacheKey = generateResponseCacheKey(
      requestPayload.messages,
      modelIdInDb,
      requestPayload.temperature
    );

    const cached = responseCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < RESPONSE_CACHE_TTL) {
      console.log(`[ResponseCache] Cache HIT for ${providerName}/${modelIdInDb} - returning cached response`);
      return {
        success: true,
        response: undefined,
        responseData: cached.response,
        responseHeaders: new Headers({ 'x-rokey-cache': 'hit' }),
        status: 200,
        error: null,
        llmRequestTimestamp: new Date(),
        llmResponseTimestamp: new Date(),
      };
    } else {
      console.log(`[ResponseCache] Cache MISS for ${providerName}/${modelIdInDb} - proceeding with API call`);
    }
  }

  // Enhanced fetch configuration with better error handling
  const fetchOptions: RequestInit = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Connection': 'keep-alive',
      'Keep-Alive': 'timeout=30, max=100',
      'User-Agent': 'RoKey/1.0 (Performance-Optimized)',
      'Accept': 'application/json',
      'Cache-Control': 'no-cache',
    } as Record<string, string>,
  };

  try {
    // Determine the actual model ID to be used for the provider API call
    const actualModelIdForProvider = getDirectProviderModelId(modelIdInDb, providerName || '');

    // OpenRouter uses the modelIdInDb directly as its `model` parameter, which might be like "google/gemini-pro"
    // Other providers expect just the part after the prefix, e.g., "gemini-pro"
    const effectiveModelId = providerName?.toLowerCase() === 'openrouter' ? modelIdInDb : actualModelIdForProvider;

    if (!effectiveModelId) {
       throw { message: `Effective model ID is missing for provider ${providerName} (DB Model: ${modelIdInDb})`, status: 500, internal: true };
    }

    console.log(`[executeProviderRequest] Calling Provider: ${providerName}, Model: ${effectiveModelId}`);
    p_llmRequestTimestamp = new Date();

    if (providerName?.toLowerCase() === 'openai') {
      const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;
      const payload = { ...providerAPIPayload, model: effectiveModelId, messages: requestPayload.messages, stream: requestPayload.stream };
      Object.keys(payload).forEach((key: string) => (payload as any)[key] === undefined && delete (payload as any)[key]);

      // Use clean options object (avoid problematic headers)
      const openaiOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKeyToUse}`,
          'User-Agent': 'RoKey/1.0',
          'Origin': 'https://rokey.app',
          'Cache-Control': 'no-cache',
        },
        body: JSON.stringify(payload)
      };

      console.log(`[OpenAI] Attempting connection to OpenAI API...`);
      const rawResponse = await robustFetch('https://api.openai.com/v1/chat/completions', openaiOptions);
      p_llmResponseTimestamp = new Date(); p_status = rawResponse.status; p_responseHeaders = rawResponse.headers;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        p_error = { message: `OpenAI Error: ${err?.error?.message || rawResponse.statusText}`, status: rawResponse.status, provider_error: err };
        throw p_error;
      }
      if (requestPayload.stream) {
          if (!rawResponse.body) { p_error = { message: 'OpenAI stream body null', status: 500 }; throw p_error; }
          p_response = rawResponse; // Return raw stream response (already SSE)
          p_responseData = { note: "streamed" }; // For logging consistency
      } else {
          p_responseData = await rawResponse.json();
      }

    } else if (providerName?.toLowerCase() === 'openrouter') {
      const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;
      const payload = {
        ...providerAPIPayload,
        model: effectiveModelId,
        messages: requestPayload.messages,
        stream: requestPayload.stream,
        usage: { include: true } // Enable OpenRouter usage accounting for cost tracking
      };
      Object.keys(payload).forEach((key: string) => (payload as any)[key] === undefined && delete (payload as any)[key]);

      // Use clean options object like Google (avoid problematic headers)
      const openrouterOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKeyToUse}`,
          'HTTP-Referer': 'https://rokey.app',
          'X-Title': 'RoKey',
          'User-Agent': 'RoKey/1.0',
          'Origin': 'https://rokey.app',
          'Cache-Control': 'no-cache',
        },
        body: JSON.stringify(payload)
      };

      console.log(`[OpenRouter] Attempting connection to OpenRouter API...`);
      const rawResponse = await robustFetch('https://openrouter.ai/api/v1/chat/completions', openrouterOptions);
      p_llmResponseTimestamp = new Date(); p_status = rawResponse.status; p_responseHeaders = rawResponse.headers;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        p_error = { message: `OpenRouter Error: ${err?.error?.message || rawResponse.statusText}`, status: rawResponse.status, provider_error: err };
        throw p_error;
      }

      if (requestPayload.stream) {
          if (!rawResponse.body) { p_error = { message: 'OpenRouter stream body null', status: 500 }; throw p_error; }
          p_response = rawResponse;
          p_responseData = { note: "streamed" };
      } else {
          p_responseData = await rawResponse.json();
      }

    } else if (providerName?.toLowerCase() === 'google') {
      // Transform model ID: remove 'models/' prefix for OpenAI endpoint
      const openAIModelId = effectiveModelId?.replace(/^models\//, '') || effectiveModelId;
      const googleApiUrl = `https://generativelanguage.googleapis.com/v1beta/openai/chat/completions`;

      const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;
      const googlePayload = {
        model: openAIModelId,
        messages: requestPayload.messages,
        stream: requestPayload.stream || false
      };

      // Add optional parameters if they exist
      if (requestPayload.temperature !== undefined) googlePayload.temperature = requestPayload.temperature;
      if (requestPayload.max_tokens !== undefined) googlePayload.max_tokens = requestPayload.max_tokens;

      const googleFetchOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKeyToUse}`,
          'User-Agent': 'RoKey/1.0',
          'Origin': 'https://rokey.app',
          'Cache-Control': 'no-cache',
        },
        body: JSON.stringify(googlePayload)
      };

      console.log(`[Google] Attempting connection to Google Gemini OpenAI-compatible API...`);
      const rawResponse = await robustFetch(googleApiUrl, googleFetchOptions);
      p_llmResponseTimestamp = new Date();
      p_status = rawResponse.status;
      p_responseHeaders = rawResponse.headers;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        let errMsg = err?.error?.message || rawResponse.statusText;
        if (Array.isArray(err) && err[0]?.error?.message) errMsg = err[0].error.message;
        p_error = { message: `Google Error: ${errMsg}`, status: rawResponse.status, provider_error: err };
        throw p_error;
      }

      if (requestPayload.stream) {
        if (!rawResponse.body) {
          p_error = { message: 'Google stream body null', status: 500 };
          throw p_error;
        }
        p_response = rawResponse;
        p_responseData = { note: "streamed" };
      } else {
        p_responseData = await rawResponse.json();
      }

    } else if (providerName?.toLowerCase() === 'anthropic') {
      const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;
      const payload = {
        ...providerAPIPayload,
        model: effectiveModelId,
        messages: requestPayload.messages,
        stream: requestPayload.stream
      };
      Object.keys(payload).forEach((key: string) => (payload as any)[key] === undefined && delete (payload as any)[key]);

      // Use clean options object (avoid problematic headers)
      const anthropicOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKeyToUse}`,
          'User-Agent': 'RoKey/1.0',
          'Origin': 'https://rokey.app',
          'Cache-Control': 'no-cache',
        },
        body: JSON.stringify(payload)
      };

      console.log(`[Anthropic] Attempting connection to Anthropic OpenAI-compatible API...`);
      const rawResponse = await robustFetch('https://api.anthropic.com/v1/chat/completions', anthropicOptions);
      p_llmResponseTimestamp = new Date();
      p_status = rawResponse.status;
      p_responseHeaders = rawResponse.headers;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        p_error = { message: `Anthropic Error: ${err?.error?.message || rawResponse.statusText}`, status: rawResponse.status, provider_error: err };
        throw p_error;
      }

      if (requestPayload.stream) {
        if (!rawResponse.body) {
          p_error = { message: 'Anthropic stream body null', status: 500 };
          throw p_error;
        }
        p_response = rawResponse;
        p_responseData = { note: "streamed" };
      } else {
        p_responseData = await rawResponse.json();
      }

    } else if (providerName?.toLowerCase() === 'deepseek') {
      const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;
      const payload = {
        ...providerAPIPayload,
        model: effectiveModelId,
        messages: requestPayload.messages,
        stream: requestPayload.stream
      };
      Object.keys(payload).forEach((key: string) => (payload as any)[key] === undefined && delete (payload as any)[key]);

      // Use clean options object (avoid problematic headers)
      const deepseekOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKeyToUse}`,
          'User-Agent': 'RoKey/1.0',
          'Origin': 'https://rokey.app',
          'Cache-Control': 'no-cache',
        },
        body: JSON.stringify(payload)
      };

      console.log(`[DeepSeek] Attempting connection to DeepSeek API...`);
      const rawResponse = await robustFetch('https://api.deepseek.com/v1/chat/completions', deepseekOptions);
      p_llmResponseTimestamp = new Date();
      p_status = rawResponse.status;
      p_responseHeaders = rawResponse.headers;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        p_error = { message: `DeepSeek Error: ${err?.error?.message || rawResponse.statusText}`, status: rawResponse.status, provider_error: err };
        throw p_error;
      }

      if (requestPayload.stream) {
        if (!rawResponse.body) {
          p_error = { message: 'DeepSeek stream body null', status: 500 };
          throw p_error;
        }
        p_response = rawResponse;
        p_responseData = { note: "streamed" };
      } else {
        p_responseData = await rawResponse.json();
      }

    } else if (providerName?.toLowerCase() === 'xai') {
      const { custom_api_config_id, role, ...providerAPIPayload } = requestPayload;
      const payload = {
        ...providerAPIPayload,
        model: effectiveModelId,
        messages: requestPayload.messages,
        stream: requestPayload.stream || false
      };
      Object.keys(payload).forEach((key: string) => (payload as any)[key] === undefined && delete (payload as any)[key]);

      // Use clean options object (avoid problematic headers)
      const xaiOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKeyToUse}`,
          'User-Agent': 'RoKey/1.0',
          'Origin': 'https://rokey.app',
          'Cache-Control': 'no-cache',
        },
        body: JSON.stringify(payload)
      };

      console.log(`[XAI] Attempting connection to XAI/Grok API...`);
      const rawResponse = await robustFetch('https://api.x.ai/v1/chat/completions', xaiOptions);
      p_llmResponseTimestamp = new Date();
      p_status = rawResponse.status;
      p_responseHeaders = rawResponse.headers;

      if (!rawResponse.ok) {
        const err = await rawResponse.json().catch(() => ({error:{message:rawResponse.statusText}}));
        p_error = { message: `XAI/Grok Error: ${err?.error?.message || rawResponse.statusText} (Type: ${err?.error?.type})`, status: rawResponse.status, provider_error: err };
        throw p_error;
      }

      if (requestPayload.stream) {
        if (!rawResponse.body) {
          p_error = { message: 'XAI stream body null', status: 500 };
          throw p_error;
        }
        p_response = rawResponse;
        p_responseData = { note: "streamed" };
      } else {
        p_responseData = await rawResponse.json();
      }

    } else {
      p_error = { message: `Provider '${providerName}' is configured but not supported by RoKey proxy (executeProviderRequest).`, status: 501, internal: true };
      throw p_error;
    }

    // Cache successful non-streaming responses
    if (!requestPayload.stream && p_responseData && requestPayload.messages && modelIdInDb) {
      const cacheKey = generateResponseCacheKey(
        requestPayload.messages,
        modelIdInDb,
        requestPayload.temperature
      );

      responseCache.set(cacheKey, {
        response: p_responseData,
        timestamp: Date.now(),
        provider: providerName || 'unknown',
        model: modelIdInDb
      });

      // Cleanup cache if needed
      cleanupResponseCache();

      console.log(`[ResponseCache] Cached response for ${providerName}/${modelIdInDb} (cache size: ${responseCache.size})`);
    }

    return {
      success: true,
      response: p_response,
      responseData: p_responseData,
      responseHeaders: p_responseHeaders,
      status: p_status,
      error: null, // Explicitly null on success
      llmRequestTimestamp: p_llmRequestTimestamp!,
      llmResponseTimestamp: p_llmResponseTimestamp!,
    };

  } catch (errorCaught: any) {
    // Enhanced error handling with network diagnostics
    const finalError = p_error || errorCaught;

    // Add network diagnostic information
    let errorType = 'ProviderCommsError';
    let diagnosticInfo = '';

    if (errorCaught.name === 'AbortError') {
      errorType = 'TimeoutError';
      diagnosticInfo = 'Request timed out after 30 seconds';
    } else if (errorCaught.message?.includes('fetch failed')) {
      errorType = 'NetworkError';
      diagnosticInfo = 'Network connection failed - check internet connectivity';
    } else if (errorCaught.code === 'ENOTFOUND') {
      errorType = 'DNSError';
      diagnosticInfo = 'DNS resolution failed - check network settings';
    } else if (errorCaught.code === 'ECONNREFUSED') {
      errorType = 'ConnectionRefused';
      diagnosticInfo = 'Connection refused by server';
    }

    console.error(`[executeProviderRequest] ${errorType} for provider ${providerName}, model ${modelIdInDb}. Status: ${finalError.status}. Message: ${finalError.message}. Diagnostic: ${diagnosticInfo}`, finalError.provider_error);

    return {
      success: false,
      status: finalError.status || 500,
      error: finalError.provider_error || {
        message: `${finalError.message}${diagnosticInfo ? ` (${diagnosticInfo})` : ''}`,
        type: finalError.internal ? 'RoKeyInternal' : errorType,
        diagnostic: diagnosticInfo
      },
      llmRequestTimestamp: p_llmRequestTimestamp || new Date(),
      llmResponseTimestamp: p_llmResponseTimestamp || new Date(),
      response: undefined,
      responseData: undefined,
      responseHeaders: p_responseHeaders, // Might have headers even on error
    };
  }
}
